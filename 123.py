import os
import numpy as np
import pandas as pd
import pymc as pm
import pytensor.tensor as pt
import arviz as az
import matplotlib.pyplot as plt
import corner
from scipy import integrate
from astropy import units as u
from astropy import constants as const
from astropy.table import Table
from astropy.cosmology import FlatLambdaCDM
# 1.定义宇宙学参数 (Planck 2018)
H0 = 67.4 * u.km / (u.s * u.Mpc)  # Hubble constant
Om0 = 0.315  # 物质密度参数（无量纲）

# 创建全局宇宙学模型
cosmo = FlatLambdaCDM(H0=H0, Om0=Om0)

def print_cosmology():
    """打印当前使用的宇宙学参数"""
    print("Hubble constant in default cosmology, H0: ", cosmo.H0, " [km/s/Mpc]")
    print("Matter density parameter, Om0: ", cosmo.Om0)
    print("Dark energy density parameter, Ode0: ", cosmo.Ode0)

# 其他常量定义
c_light = const.c.to(u.km/u.s)  # 光速
prefactor = 979 * u.pc / (u.cm**3)  # DM_IGM 预因子

# 2. 加载数据
def load_data():
    """加载并预处理FRB数据，确保单位一致性"""
    # 使用pandas读取CSV文件，然后转换为astropy Table
    df = pd.read_csv('Select localized FRBs DM.csv')
    table = Table.from_pandas(df)
    
    # 添加单位信息
    dm_unit = u.pc / (u.cm**3)  # 定义DM单位
    table['DM_obs(pc cm^-3)'] = table['DM_obs(pc cm^-3)'] * dm_unit
    table['DM_MW_NE2001(pc cm^-3)'] = table['DM_MW_NE2001(pc cm^-3)'] * dm_unit
    table['sigma_DM_obs(pc cm^-3)'] = table['sigma_DM_obs(pc cm^-3)'] * dm_unit
    table['dL(Gpc)'] = table['dL(Gpc)'] * u.Gpc
    table['dL_error(Gpc)'] = table['dL_error(Gpc)'] * u.Gpc
    
    # 计算 DM_ext 和误差
    table['DM_ext_obs'] = table['DM_obs(pc cm^-3)'] - table['DM_MW_NE2001(pc cm^-3)']
    
    # 正确处理误差计算，确保单位一致性
    sigma_obs = table['sigma_DM_obs(pc cm^-3)'].value
    sigma_mw = 30.0  # NE2001模型的固定误差
    table['sigma_ext'] = np.sqrt(sigma_obs**2 + sigma_mw**2) * dm_unit
    
    # 转换光度距离到Mpc
    table['dL'] = table['dL(Gpc)'].to(u.Mpc)
    table['dL_error'] = table['dL_error(Gpc)'].to(u.Mpc)
    
    return table
# 3. 辅助函数定义
def sfr_madau_dickinson(z):
    """计算给定红移处的星系形成率（返回无量纲比值）"""
    return 0.015 * (1+z)**2.7 / (1+(1+z)/2.9)**5.6

# 5. MCMC模型定义
def run_mcmc():
    # 加载数据
    table = load_data()
    z_values = table['z']  # 无量纲
    
    # 提取数据并确保单位正确
    DM_ext_obs = table['DM_ext_obs'].value  # pc/cm^3
    sigma_ext = table['sigma_ext'].value  # pc/cm^3
    dL_obs = table['dL'].value  # Mpc
    dL_error = table['dL_error'].value  # Mpc
    
    # 预计算SFR比值（无量纲）
    SFR0 = sfr_madau_dickinson(0)
    SFR_z = sfr_madau_dickinson(z_values)
    
    # 使用astropy.cosmology计算光度距离
    dL_th_astropy = cosmo.luminosity_distance(z_values).to(u.Mpc).value
    
    with pm.Model() as model:
        # 先验分布（Priors）
        f_IGM_0 = pm.Uniform('f_IGM_0', 0., 1.0)  # 无量纲
        alpha = pm.Uniform('alpha', 0.0, 4.0)  # 无量纲
        DM_host_loc_0 = pm.Uniform('DM_host_loc_0', 0, 300)  # pc/cm^3
        sigma_int = pm.Uniform('sigma_int', 0, 100.0)  # pc/cm^3
        
        # 计算主要量
        DM_host = DM_host_loc_0 * pt.sqrt(SFR_z / SFR0)  # pc/cm^3
        
        # 使用简化的理论模型
        # DM_IGM ∝ f_IGM_0 * (1 + alpha * z)
        DM_IGM_th = prefactor.to(u.pc/u.cm**3).value * f_IGM_0 * (1 + alpha * z_values)  # pc/cm^3
        
        # 计算理论DM_ext (pc/cm^3)
        DM_ext_th = DM_IGM_th + DM_host / (1 + z_values)
        
        # 计算总误差 (pc/cm^3)
        sigma_tot = pt.sqrt(sigma_ext**2 + sigma_int**2)
        
        # 似然函数（Likelihood） - 直接比较观测和理论的DM_ext
        pm.Normal('likelihood', mu=DM_ext_th, sigma=sigma_tot, observed=DM_ext_obs)
        
        # 抽样器采样
        trace = pm.sample(
            draws=1000,
            tune=1000,
            chains=4,# 同时采样链数
            target_accept=0.95,
            return_inferencedata=True,
            cores=4,#采样使用的cpu核心数
        )
        
    return trace

# 6. 后验分析绘图函数
def plot_results(trace):
    """绘制MCMC结果，包括轨迹图、后验分布和corner图"""
    # 绘制后验分布和轨迹
    az.plot_trace(trace, kind="rank_vlines", var_names=['f_IGM_0', 'alpha', 'DM_host_loc_0', 'sigma_int'])
    plt.tight_layout()
    plt.show()
    
    # 打印参数估计结果
    summary = az.summary(trace, var_names=['f_IGM_0', 'alpha', 'DM_host_loc_0', 'sigma_int'])
    print("\n参数估计结果:")
    print(summary)
    
    # 绘制corner图
    # 从trace中提取样本
    samples = np.column_stack([
        trace.posterior.f_IGM_0.values.flatten(),
        trace.posterior.alpha.values.flatten(),
        trace.posterior.DM_host_loc_0.values.flatten(),
        trace.posterior.sigma_int.values.flatten()
    ])
    
    # 设置参数标签（包含单位信息）
    labels = [
        r'$f_{\rm IGM,0}$', 
        r'$\alpha$', 
        r'${\rm DM_{host,loc,0}}$ ', 
        r'$\sigma_{\rm int}$'
    ]
    
    # 绘制corner图
    fig = corner.corner(
        samples,
        labels=labels,
        quantiles=[0.16, 0.5, 0.84],  # 显示16%、50%和84%分位数
        show_titles=True,  # 显示边缘分布的统计信息
        title_fmt='.3f',  # 标题数字格式
        title_kwargs={'fontsize': 12},
        label_kwargs={'fontsize': 12},
        hist_kwargs={'density': True}
    )
    plt.show()

if __name__ == "__main__":
    # 运行MCMC
    trace = run_mcmc()
    
    # 绘制结果
    plot_results(trace)
