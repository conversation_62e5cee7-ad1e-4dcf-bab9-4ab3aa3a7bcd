import numpy as np
from astropy.table import Table
import sys

def process_pantheon_data(input_file='Pantheon+SH0ES.dat', output_file='zdL_SNIa.txt'):
    """
    处理 Pantheon+SH0ES 数据，计算光度距离及误差 (单位: Mpc)
    
    参数:
        input_file (str): 输入数据文件路径
        output_file (str): 输出文件路径
    """
    try:
        # 读取数据
        data = Table.read(input_file, format='ascii')

        # 检查必需列
        required_columns = ['zHD', 'MU_SH0ES', 'MU_SH0ES_ERR_DIAG']
        missing_cols = [col for col in required_columns if col not in data.colnames]
        if missing_cols:
            raise ValueError(f"文件缺少必需列: {missing_cols}")

        # 计算光度距离 (单位: Mpc)
        # dL = 10^((mu - 25) / 5) Mpc
        # MU_SH0ES 对应 mu
        mu = data['MU_SH0ES']
        dL = 10**((mu - 25) / 5)

        # 计算光度距离误差 (单位: Mpc)
        # sigma_dL = (ln(10)/5) * dL * sigma_mu
        # MU_SH0ES_ERR_DIAG 对应 sigma_mu
        sigma_mu = data['MU_SH0ES_ERR_DIAG']
        dL_err = (np.log(10) / 5) * dL * sigma_mu

        # 提取结果并保存
        result = Table([data['zHD'], dL, dL_err], names=('zHD', 'dL', 'dL_err'))
        
        # 检查异常值 (NaN 或 Inf)
        has_nan = False
        has_inf = False
        for col_name in ['dL', 'dL_err']:
            column_data = result[col_name]
            # Astropy columns might be masked. Check data part.
            if hasattr(column_data, 'mask') and np.any(column_data.mask):
                has_nan = True # Masked values are like NaNs in this context
            if np.any(np.isnan(column_data.data if hasattr(column_data, 'data') else column_data)):
                has_nan = True
            if np.any(np.isinf(column_data.data if hasattr(column_data, 'data') else column_data)):
                has_inf = True
        
        if has_nan:
            print("警告: 结果中存在空值 (NaN 或 masked)!")
        if has_inf:
            print("警告: 结果中存在无穷大值!")
        
        result.write(output_file, format='ascii.tab', overwrite=True)
        
        # 打印统计信息
        print(f"处理完成！结果保存至 {output_file}")
        print(f"数据点总数: {len(result)}")
        print("备注: 光度距离单位为 Mpc (百万秒差距)")
        print("\n结果示例:")
        # 打印前5行，如果行数不足5，则打印所有行
        result.pprint(max_lines=5 if len(result) > 5 else -1, max_width=-1)

    except FileNotFoundError:
        print(f"错误: 文件 {input_file} 不存在！")
        sys.exit(1)
    except Exception as e:
        print(f"处理失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    process_pantheon_data()