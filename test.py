import os
import numpy as np
import pandas as pd
import emcee
import corner
import matplotlib.pyplot as plt
from scipy import integrate
from astropy import units as u
from astropy.table import Table
from astropy.cosmology import Planck18 as P
from astropy import constants as con

from multiprocessing import Pool
import warnings

# 2. 辅助函数

def sigma_delta_igm(z):
    """计算IGM色散量的涨落误差"""
    return 173.8 * z**0.4

def calculate_dm_igm_obs(dm_obs, dm_mw_ism, z, e_mu, sigma_host, dm_mw_halo=50.0):
    """
    计算修正后的IGM色散量 (DM_IGM_obs)

    问题修正：
    1. 原代码每次调用都重新采样宿主DM，这在MCMC中会导致不一致性
    2. 应该使用期望值而不是随机采样
    """
    # 使用对数正态分布的期望值而不是随机采样
    # 对数正态分布的期望值 = exp(μ + σ²/2)
    # 但这里e_mu已经是exp(μ)，所以期望值 = e_mu * exp(σ²/2)
    dm_host_com_expected = e_mu * np.exp(sigma_host**2 / 2)

    # 校正到观测层面
    dm_host = dm_host_com_expected / (1 + z)

    dm_igm_obs = dm_obs - dm_mw_ism - dm_mw_halo - dm_host

    # 检查是否有负值或过小的值
    if np.any(dm_igm_obs <= 0):
        warnings.warn("Warning: Some DM_IGM_obs values are negative or zero")

    return dm_igm_obs

def calculate_sigma_dm_igm_obs(sigma_obs, z, e_mu, sigma_host):
    """
    计算观测IGM色散量总误差 (sigma_DM_IGM_obs)

    问题修正：
    1. 修正了对数正态分布方差的计算
    2. 确保所有误差项都是正值
    """
    sigma_mw = 30  # pc/cm³
    sigma_igm = sigma_delta_igm(z)

    # 对数正态分布的方差：var = (exp(σ²) - 1) * exp(2μ + σ²)
    # 这里e_mu = exp(μ)，所以 var = (exp(σ²) - 1) * e_mu² * exp(σ²)
    var_host = (np.exp(sigma_host**2) - 1) * e_mu**2 * np.exp(sigma_host**2)

    # 折算到观测层面
    sigma_host_corr = np.sqrt(var_host) / (1 + z)

    error_squared = (
        sigma_obs**2 +
        sigma_mw**2 +
        sigma_igm**2 +
        sigma_host_corr**2
    )

    return np.sqrt(error_squared)

def dmigm_integrand(z_prime, alpha, f_igm_0, fe=7/8.):
    """
    IGM DM积分被积函数

    问题修正：
    1. 修正了f_IGM的计算公式，应该是相对于当前红移的修正
    2. 确保数值稳定性
    """
    # 修正的IGM电离分数，考虑演化
    figm = f_igm_0 * (1 + alpha * z_prime)

    # 确保f_IGM在物理范围内
    figm = np.clip(figm, 0.0, 1.0)

    # 计算被积函数
    E_z = np.sqrt(P.Om(0) * (1 + z_prime)**3 + P.Ode(0))
    y = (1 + z_prime) * figm * fe / E_z

    return y

def calculate_dm_igm_th(z, f_igm_0, alpha):
    """
    计算理论IGM色散量

    问题修正：
    1. 添加了数值积分的错误处理
    2. 确保积分收敛
    """
    # 计算常数A
    A = 3 * con.c * P.Ob(0) * P.H0 / (8 * np.pi * con.G * con.m_p)

    try:
        # 数值积分，增加积分精度
        integral_result, error = integrate.quad(
            dmigm_integrand, 0, z,
            args=(alpha, f_igm_0),
            epsabs=1e-10, epsrel=1e-8
        )

        # 检查积分误差
        if error > 1e-6 * abs(integral_result):
            warnings.warn(f"Large integration error at z={z}: {error}")

        val = (A * integral_result).to(u.pc * u.cm**-3).value

        # 确保结果为正
        if val <= 0:
            warnings.warn(f"Non-positive DM_IGM_th at z={z}: {val}")
            return 1e-10  # 返回一个很小的正值

        return val

    except Exception as e:
        warnings.warn(f"Integration failed at z={z}: {e}")
        return 1e-10



# 2. 加载数据
def load_data():
    """
    读取数据并进行质量控制

    问题修正：
    1. 添加了数据质量检查
    2. 更合理的筛选条件
    3. 处理异常值
    """
    table = Table.read('117 localized FRBs test.csv')

    print(f"原始数据点数: {len(table)}")

    # 应用筛选条件
    # 1. 基本的DM筛选
    dm_excess = table['DM_obs'].value - table['DM_MW_ISM'].value
    mask1 = dm_excess > 80

    # 2. 红移筛选（避免过低红移的不确定性）
    mask2 = table['z'].value > 0.01

    # 3. 误差筛选（避免过大的误差）
    mask3 = table['sigma_DM_obs'].value < 50  # 合理的DM误差上限
    mask4 = table['dL_error'].value / table['dL'].value < 0.1  # 相对误差小于10%

    # 4. 物理合理性检查
    mask5 = table['DM_obs'].value > table['DM_MW_ISM'].value + 50  # 确保有足够的IGM贡献

    combined_mask = mask1 & mask2 & mask3 & mask4 & mask5
    table = table[combined_mask]

    print(f"筛选后数据点数: {len(table)}")

    if len(table) < 10:
        raise ValueError("筛选后数据点太少，无法进行可靠的拟合")

    # 提取数据
    z = table['z'].value
    dm_obs = table['DM_obs'].value
    sigma_dm_obs = table['sigma_DM_obs'].value
    dm_mw_ism = table['DM_MW_ISM'].value
    dL_obs = table['dL'].value         # dL 单位为 Mpc
    sigma_dL_obs = table['dL_error'].value # dL_error 单位为 Mpc

    # 数据质量报告
    print(f"红移范围: {z.min():.4f} - {z.max():.4f}")
    print(f"DM_obs范围: {dm_obs.min():.1f} - {dm_obs.max():.1f} pc/cm³")
    print(f"平均DM误差: {np.mean(sigma_dm_obs):.2f} pc/cm³")

    return z, dm_obs, sigma_dm_obs, dm_mw_ism, dL_obs, sigma_dL_obs



# 3. 定义先验
def log_prior(theta):
    """
    定义参数的先验概率
    """
    f_IGM_0, alpha, e_mu, sigma_host = theta
    # 使用用户指定的先验边界
    if 0.0 < f_IGM_0 < 1.0 and -2.0 < alpha < 2.0 and 20.0 < e_mu < 200.0 and 0.2 < sigma_host < 2.0:
        return 0.0
    return -np.inf

# 4. 定义似然函数
def log_likelihood(theta, dm_obs, dm_mw_ism, z, sigma_dm_obs, dL_obs, sigma_dL_obs):
    """
    计算对数似然函数

    问题修正：
    1. 修正了似然函数的计算，按照论文中的公式
    2. 添加了数值稳定性检查
    3. 正确处理了误差传播
    """
    f_igm_0, alpha, e_mu, sigma_host = theta

    try:
        # 计算修正后的IGM观测DM及误差
        dm_igm_obs = calculate_dm_igm_obs(dm_obs, dm_mw_ism, z, e_mu, sigma_host)
        sigma_dm_igm_obs = calculate_sigma_dm_igm_obs(sigma_dm_obs, z, e_mu, sigma_host)

        # 检查是否有无效值
        if np.any(dm_igm_obs <= 0):
            return -np.inf

        # 计算理论值
        dm_igm_th = np.array([calculate_dm_igm_th(zi, f_igm_0, alpha) for zi in z])
        dL_th = P.luminosity_distance(z).to(u.Mpc).value

        # 按照论文公式计算比值
        ratio_obs = dL_obs / dm_igm_obs
        ratio_th = dL_th / dm_igm_th

        # 计算总误差 sigma_tot (按照论文公式)
        sigma_tot_squared = (
            (sigma_dL_obs / dm_igm_obs)**2 +
            (dL_obs * sigma_dm_igm_obs / dm_igm_obs**2)**2
        )
        sigma_tot = np.sqrt(sigma_tot_squared)

        # 检查误差是否合理
        if np.any(sigma_tot <= 0) or np.any(~np.isfinite(sigma_tot)):
            return -np.inf

        # 计算卡方
        chi_squared = np.sum(((ratio_obs - ratio_th) / sigma_tot)**2)

        # 计算对数似然（不包括归一化常数）
        log_like = -0.5 * chi_squared

        # 检查结果是否有限
        if not np.isfinite(log_like):
            return -np.inf

        return log_like

    except Exception as e:
        warnings.warn(f"Error in likelihood calculation: {e}")
        return -np.inf

def log_prob(theta, dm_obs, dm_mw_ism, z, sigma_dm_obs, dL_obs, sigma_dL_obs):
    """
    计算对数后验概率
    """
    # 检查先验
    lp = log_prior(theta)
    if not np.isfinite(lp):
        return -np.inf

    # 计算似然
    ll = log_likelihood(theta, dm_obs, dm_mw_ism, z, sigma_dm_obs, dL_obs, sigma_dL_obs)

    return lp + ll
# 5. 主函数
def main():
    """
    主函数：执行MCMC采样和结果分析

    问题修正：
    1. 改进了初始化策略
    2. 增加了收敛性检查
    3. 添加了结果统计和诊断
    """
    # 加载观测数据
    z, dm_obs, sigma_dm_obs, dm_mw_ism, dL_obs, sigma_dL_obs = load_data()

    # MCMC 参数设置
    ndim = 4
    nwalkers = 32
    nsteps = 2000  # 增加步数以确保收敛

    # 改进的初始化策略：在先验范围内更合理地初始化
    np.random.seed(42)  # 设置随机种子以便重现
    pos = np.vstack([
        np.random.uniform(0.5, 0.9, size=nwalkers),     # f_IGM_0 偏向较高值
        np.random.uniform(-0.5, 0.5, size=nwalkers),    # alpha 偏向0附近
        np.random.uniform(50.0, 150.0, size=nwalkers),  # e_mu 偏向中等值
        np.random.uniform(0.5, 1.5, size=nwalkers)      # sigma_host 偏向中等值
    ]).T

    print("开始MCMC采样...")

    # 运行 emcee 采样
    with Pool() as pool:
        sampler = emcee.EnsembleSampler(
            nwalkers, ndim, log_prob,
            args=(dm_obs, dm_mw_ism, z, sigma_dm_obs, dL_obs, sigma_dL_obs),
            pool=pool
        )
        sampler.run_mcmc(pos, nsteps, progress=True)

    print("MCMC采样完成")

    # 检查收敛性
    tau = sampler.get_autocorr_time(quiet=True)
    print(f"自相关时间: {tau}")

    # 动态确定burn-in长度
    burnin = max(100, int(2 * np.max(tau)))
    thin = max(1, int(0.5 * np.min(tau)))

    print(f"Burn-in: {burnin}, Thinning: {thin}")

    # 扔掉 burn-in，展平链
    flat_samples = sampler.get_chain(discard=burnin, thin=thin, flat=True)

    print(f"有效样本数: {len(flat_samples)}")

    # 计算最佳拟合参数和不确定性
    labels = ["f_IGM_0", "alpha", "e_mu", "sigma_host"]
    percentiles = np.percentile(flat_samples, [16, 50, 84], axis=0)
    best_fit = percentiles[1]  # 中位数
    uncertainties = np.array([percentiles[1] - percentiles[0],
                             percentiles[2] - percentiles[1]])

    print("\n拟合结果:")
    for i, label in enumerate(labels):
        print(f"{label}: {best_fit[i]:.4f} +{uncertainties[1,i]:.4f} -{uncertainties[0,i]:.4f}")

    # 绘制 Corner 图
    fig = corner.corner(
        flat_samples, labels=labels, truths=best_fit,
        color='steelblue', quantiles=[0.16, 0.5, 0.84],
        show_titles=True, title_kwargs={"fontsize":12}
    )
    fig.savefig("corner_plot.png", dpi=300, bbox_inches='tight')
    print("Corner图已保存为 corner_plot.png")

    # 绘制链图
    fig2, axes = plt.subplots(ndim, figsize=(10, 7), sharex=True)
    samples = sampler.get_chain()
    for i in range(ndim):
        ax = axes[i]
        ax.plot(samples[:, :, i], color="k", alpha=0.3)
        ax.axvline(burnin, color='r', linestyle='--', alpha=0.7, label='Burn-in')
        ax.set_ylabel(labels[i])
        if i == 0:
            ax.legend()
    axes[-1].set_xlabel("Step number")
    fig2.savefig("chain_plot.png", dpi=300, bbox_inches='tight')
    print("链图已保存为 chain_plot.png")

    # 计算并显示一些诊断信息
    acceptance_fraction = np.mean(sampler.acceptance_fraction)
    print(f"\n诊断信息:")
    print(f"平均接受率: {acceptance_fraction:.3f}")
    print(f"推荐接受率范围: 0.2-0.5")

    if acceptance_fraction < 0.2:
        print("警告: 接受率过低，可能需要调整初始化或步长")
    elif acceptance_fraction > 0.5:
        print("警告: 接受率过高，可能需要增加步长")

    plt.show()

    return sampler, flat_samples, best_fit

if __name__ == '__main__':
    sampler, samples, best_fit = main()


