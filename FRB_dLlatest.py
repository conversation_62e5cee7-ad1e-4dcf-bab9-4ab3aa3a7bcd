import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
from sklearn.neural_network import MLPRegressor
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score
from astropy.cosmology import FlatLambdaCDM
from scipy import stats
from joblib import Parallel, delayed
import multiprocessing
try:
    import scienceplots
    plt.style.use(['science', 'ieee','notebook'])
except ImportError:
    print("scienceplots package not found, using default style")

# Set random seed for reproducibility
np.random.seed(152)

# 常量
c = 299792.458  # km/s (光速)

# 读取FRB数据
frb_file = 'Select localized FRBs DM_name.csv'
frb_df = pd.read_csv(frb_file)

print(f"FRB数据加载完成，共{len(frb_df)}个观测数据")
print(frb_df.head())

# 检查红移中的缺失值
print("\n检查红移中的缺失值:")
print(frb_df['z'].isnull().sum())

# 过滤掉红移值缺失的行
frb_df = frb_df.dropna(subset=['z'])
print(f"过滤后: {len(frb_df)}个具有有效红移值的FRB")

# 首先需要在已知的宇宙学距离数据上训练MLP模型
# 为此，我们使用包含已知红移-光度距离对的单独训练数据集
# 读取训练数据
data_file = 'zdL_result.txt'
train_df = pd.read_csv(data_file, sep='\t')

print(f"\n训练数据加载完成，共{len(train_df)}个观测数据")
print(train_df.head())

# 检查缺失值
print("\n检查训练数据中的缺失值:")
print(train_df.isnull().sum())

# 清理训练数据
train_df = train_df.replace([np.inf, -np.inf], np.nan).dropna()
print(f"清理后: {len(train_df)}个训练观测数据")

# 从训练数据中提取特征(红移)和目标(光度距离)
X = train_df['zHD'].values.reshape(-1, 1)  # 重塑以适应sklearn
y = train_df['dL'].values

# 将数据分为训练集和测试集(75%训练，25%测试)
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.25, random_state=42)
print(f"训练集大小: {len(X_train)}, 测试集大小: {len(X_test)}")

# 标准化特征以获得更好的收敛性
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

# 使用GridSearchCV对MLPRegressor进行超参数调优
print("\n开始MLPRegressor超参数调优...")
gcv = GridSearchCV(
    estimator=MLPRegressor(
        activation='relu',
        solver='lbfgs',
        learning_rate='adaptive',
        max_iter=200,
        random_state=152,
        verbose=False
    ),
    param_grid={
        'hidden_layer_sizes': np.arange(10, 250, 10)  # 简化为仅优化隐藏层大小
    },
    cv=2,                  # 使用2折交叉验证，降低计算量
    scoring='neg_mean_squared_error',
    n_jobs=-1,
    verbose=2,
    refit=True
)

# 执行网格搜索
print("\n进行网格搜索...")
gcv.fit(X_train_scaled, y_train) # 将模型拟合到训练数据

# 输出最佳参数
print("\n最佳参数组合:")
for param, value in gcv.best_params_.items():
    print(f"{param}: {value}")

# 使用最佳模型，但增加max_iter以确保收敛
best_params = gcv.best_params_
best_params['max_iter'] = 1000  # 增大最大迭代次数，确保收敛
best_params['tol'] = 1e-5       # It's better to set a tolerance value to aid convergence
mlp = MLPRegressor(**best_params, random_state=42)
print("\n使用最佳参数训练最终模型，增加max_iter以确保收敛...")
mlp.fit(X_train_scaled, y_train)

print("\n使用最佳模型评估性能:")

# 在训练和测试数据上进行预测
y_train_pred = mlp.predict(X_train_scaled)
y_test_pred = mlp.predict(X_test_scaled)

# 评估模型
train_mse = mean_squared_error(y_train, y_train_pred)
test_mse = mean_squared_error(y_test, y_test_pred)
train_r2 = r2_score(y_train, y_train_pred)
test_r2 = r2_score(y_test, y_test_pred)

print("\n模型评估:")
print(f"训练集MSE: {train_mse:.6f}")
print(f"测试集MSE: {test_mse:.6f}")
print(f"训练集R²: {train_r2:.6f}")
print(f"测试集R²: {test_r2:.6f}")
print(f"最佳隐藏层大小: {gcv.best_params_['hidden_layer_sizes']}")

# 置信度计算
def bootstrap_uncertainty(model, X, y, X_range, scaler=None, n_bootstraps=100):
    """
    使用bootstrap方法估计预测不确定性
    
    参数:
    model: 训练好的模型
    X: 原始训练数据
    y: 目标值
    X_range: 需要预测的范围
    scaler: 特征缩放器，如提供则应用于X和X_range
    n_bootstraps: bootstrap重采样次数
    
    返回:
    均值和标准差
    """
    # 获取模型参数并增加迭代次数
    model_params = model.get_params()
    model_params['max_iter'] = 1000  # 增加最大迭代次数，避免收敛警告
    
    # 应用缩放器（如果提供）
    X_scaled = X if scaler is None else scaler.transform(X)
    X_range_scaled = X_range if scaler is None else scaler.transform(X_range)
    
    preds = []
    print(f"执行 {n_bootstraps} 次Bootstrap采样...")
    
    # 使用更高效的并行计算方式
    n_jobs = min(multiprocessing.cpu_count(), 8)  # 限制最大使用CPU核心数
    
    # 定义单次Bootstrap的函数
    def _single_bootstrap(i):
        # 重采样数据
        indices = np.random.randint(0, len(X_scaled), len(X_scaled))
        X_bs, y_bs = X_scaled[indices], y[indices]
        
        # 创建并训练新模型
        bs_model = MLPRegressor(**model_params)
        bs_model.fit(X_bs, y_bs)
        
        # 预测
        return bs_model.predict(X_range_scaled)
    
    # 并行执行Bootstrap采样
    predictions = Parallel(n_jobs=n_jobs, verbose=1)(
        delayed(_single_bootstrap)(i) for i in range(n_bootstraps)
    )
    
    predictions = np.array(predictions)
    mean_predictions = np.mean(predictions, axis=0)
    std_predictions = np.std(predictions, axis=0)
    
    return mean_predictions, std_predictions

# 创建模型性能可视化
z_range = np.linspace(min(train_df['zHD']), max(train_df['zHD']), 500).reshape(-1, 1)  # 减少计算点数
z_range_scaled = scaler.transform(z_range)
d_L_pred = mlp.predict(z_range_scaled)

# 计算理论光度距离曲线进行比较
# Planck 2018宇宙学参数 (arXiv:1807.06209)
cosmo_planck = FlatLambdaCDM(H0=67.4, Om0=0.315)
dL_theory_planck = cosmo_planck.luminosity_distance(z_range.flatten()).value / 1000  # 从Mpc转换为Gpc

# SHOES测量宇宙学参数 (arXiv:2408.11770)
cosmo_shoes = FlatLambdaCDM(H0=72.6, Om0=0.315)
dL_theory_shoes = cosmo_shoes.luminosity_distance(z_range.flatten()).value / 1000  # 从Mpc转换为Gpc

# 计算预测不确定性，使用bootstrap_uncertainty方法
print("\n计算预测不确定性...")
z_subset = z_range[::5]  # 使用较少的采样点以提高计算效率
z_subset_scaled = scaler.transform(z_subset)
y_mean, y_std = bootstrap_uncertainty(
    mlp, X_train_scaled, y_train, z_subset_scaled, scaler=None, n_bootstraps=100
)

# =============== 计算FRB光度距离并保存到CSV ===============
print("\n开始计算FRB的光度距离...")

# 从FRB数据中提取红移值
frb_z = frb_df['z'].values.reshape(-1, 1)  # 重塑为(n_samples, 1)形状
# 使用训练模型时相同的scaler进行标准化
frb_z_scaled = scaler.transform(frb_z)
print(f"处理 {len(frb_z)} 个FRB红移值，范围: [{frb_z.min():.4f}, {frb_z.max():.4f}]")

# 使用MLP模型预测FRB光度距离
print("使用MLP模型预测光度距离...")
frb_dL_pred = mlp.predict(frb_z_scaled)

# 使用bootstrap方法计算每个FRB预测的不确定性
print("计算FRB预测不确定性...")
frb_uncertainties = []

# 为每个FRB计算不确定性
for z in frb_z:
    # 创建z值的小范围（仅包含该点）用于bootstrap
    z_point = np.array([z])
    z_point_scaled = scaler.transform(z_point)
    
    # 使用相同的bootstrap方法计算单个点的不确定性
    # 由于只有一个点，我们只需要第一个元素的标准差
    _, std_single = bootstrap_uncertainty(
        mlp, X_train_scaled, y_train, z_point_scaled, 
        scaler=None, n_bootstraps=100  # 减少bootstrap样本数以提高速度
    )
    frb_uncertainties.append(std_single[0])  # 只取第一个元素

frb_uncertainties = np.array(frb_uncertainties)

# 将结果添加到数据框
print("将计算结果添加到原始CSV文件...")
frb_df['dL_MLP'] = frb_dL_pred
frb_df['dL_error'] = frb_uncertainties  # 使用bootstrap不确定性作为误差
frb_df['dL_lower'] = frb_dL_pred - frb_uncertainties  # 1sigma下限
frb_df['dL_upper'] = frb_dL_pred + frb_uncertainties  # 1sigma上限

# 保存结果到原始CSV文件
frb_df.to_csv('Select localized FRBs DM_name.csv', index=False)
print("已将光度距离及误差保存到Select localized FRBs DM_name.csv")

# 显示结果摘要
print("\nFRB光度距离计算结果摘要（前10条）:")
result_summary = frb_df[['TNSname', 'z', 'dL_MLP', 'dL_error']].head(10)
print(result_summary)

# =============== FRB光度距离计算结束 ===============

# 创建主可视化图像
plt.figure(figsize=(12, 8))

# 使用bootstrap_uncertainty的结果绘制置信区间
# 先绘制2σ区间
plt.fill_between(
    z_subset.flatten(),
    y_mean - 2*y_std,
    y_mean + 2*y_std,
    color='grey', alpha=0.2, label='2σ Confidence Interval', zorder=1
)
# 再绘制1σ区间
plt.fill_between(
    z_subset.flatten(),
    y_mean - y_std,
    y_mean + y_std,
    color='grey', alpha=0.4, label='1σ Confidence Interval', zorder=2
)

# 绘制理论曲线 - 使用原始红移值
plt.plot(z_range, dL_theory_planck, 'k--', linewidth=2, label=r'$\Lambda CDM$-Planck 2018 ($H_0=67.4$ km/s/Mpc)', zorder=3)
plt.plot(z_range, dL_theory_shoes, 'b-.', linewidth=2, label=r'$\Lambda CDM$-SHOES ($H_0=72.6$ km/s/Mpc)', zorder=4)

# 绘制训练和测试数据点 - 使用原始红移值
plt.scatter(X_train, y_train, s=20, alpha=0.4, label='Training Data (SN Ia)', color='blue', zorder=5)
plt.scatter(X_test, y_test, s=20, alpha=0.6, label='Test Data (SN Ia)', color='green', zorder=6)

# 绘制MLP预测曲线 - 使用原始红移值
plt.plot(z_range, d_L_pred, 'r-', linewidth=2.5, label='Reconstructed SN Ia dL-z Relation(MLP)', zorder=9)

# 绘制FRB预测点 - 使用原始红移值
plt.scatter(frb_z, frb_dL_pred, s=40, color='magenta', marker='*', 
            label='FRB Predictions', zorder=10)
            
# 为FRB预测添加误差棒 - 使用原始红移值
for i, (z, dL, err) in enumerate(zip(frb_z, frb_dL_pred, frb_uncertainties)):
    plt.errorbar(z, dL, yerr=err, fmt='none', ecolor='magenta', alpha=0.7, capsize=3, zorder=10)

# 轴标签设置
plt.xlabel('Redshift (z)', fontsize=14)
plt.ylabel('Luminosity Distance (Gpc)', fontsize=14)

# 获取当前图例句柄和标签
handles, labels = plt.gca().get_legend_handles_labels()

# 按照指定顺序重新排列图例
order = [
    labels.index(r'$\Lambda CDM$-Planck 2018 ($H_0=67.4$ km/s/Mpc)'),
    labels.index(r'$\Lambda CDM$-SHOES ($H_0=72.6$ km/s/Mpc)'),
    labels.index('Training Data (SN Ia)'),
    labels.index('Test Data (SN Ia)'),
    labels.index('Reconstructed SN Ia dL-z Relation(MLP)'),
    labels.index('1σ Confidence Interval'),
    labels.index('2σ Confidence Interval'),
    labels.index('FRB Predictions')
]

# 添加图例（按指定顺序）
plt.legend([handles[i] for i in order], [labels[i] for i in order], 
           loc='upper left', frameon=True, edgecolor='black', facecolor='white', 
           framealpha=1, fontsize=12, ncol=1)

# 改进坐标轴范围
plt.xlim(left=0)  # 从z=0开始
y_max = max(np.max(d_L_pred), np.max(frb_dL_pred) + np.max(frb_uncertainties)) * 1.1
plt.ylim(top=y_max)

# 保存高分辨率图像
plt.savefig('luminosity_distance_neural_network_reconstruction.png', dpi=300, bbox_inches='tight')
print("\n综合图像已保存为 'luminosity_distance_neural_network_reconstruction.png'")

# 保存模型和scaler
try:
    import joblib
    joblib.dump(mlp, 'mlp_zdL_model.pkl')
    joblib.dump(scaler, 'mlp_zdL_scaler.pkl')
    print("模型和scaler已保存为 'mlp_zdL_model.pkl' 和 'mlp_zdL_scaler.pkl'")
except:
    print("无法保存模型 - 可能未安装joblib。")

# 展示图形
plt.show()

