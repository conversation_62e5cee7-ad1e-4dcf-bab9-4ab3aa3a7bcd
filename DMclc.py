#!/usr/bin/env python3
"""
使用 NE2001 和 YMW16 模型计算银河系色散测量值 (DM_MW)，
"""
import pygedm
import astropy.units as u
import astropy.coordinates as c
from astropy.table import Table

table = Table.read('117 localized FRBs_name.csv', format='csv')
# 处理可能的 Unicode 负号，并将 RA/DEC 列转换为带单位的 Quantity
ra = [float(str(val).replace('−','-')) * u.deg for val in table['RA(deg)']]
dec = [float(str(val).replace('−','-')) * u.deg for val in table['DEC(deg)']]
coords = c.SkyCoord(ra=ra,
                   dec=dec,
                   frame='icrs')
# 提取银河坐标
l, b = coords.galactic.l.deg, coords.galactic.b.deg
# 以30000 pc (即 30 kpc) 为积分距离
distance = 30000 * u.pc
# 批量计算 DM
table['DM_MW_NE2001(pc/cm^3)'] = [pygedm.dist_to_dm(li, bi, distance, method='ne2001')[0].value for li, bi in zip(l, b)]
table['DM_MW_YMW16(pc/cm^3)'] = [pygedm.dist_to_dm(li, bi, distance, method='ymw16')[0].value for li, bi in zip(l, b)]
# 计算银河晕 DM 贡献
table['DM_halo(pc/cm^3)'] = [pygedm.calculate_halo_dm(gl=li, gb=bi).value for li, bi in zip(l, b)]
# 保存结果
table.write('117 localized FRBs_name.csv', format='csv', overwrite=True)
print('计算完成：已保存至 117 localized FRBs_name.csv')
