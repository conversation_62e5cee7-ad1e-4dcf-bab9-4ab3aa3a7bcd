import os
import numpy as np
import pandas as pd
import emcee
import corner
import matplotlib.pyplot as plt
from scipy import integrate
from astropy import units as u
from astropy import constants as const
from astropy.table import Table
from astropy.cosmology import Planck18 as P
from astropy import constants as const_astr

# 2. 辅助函数
def SFR_ratio(z):
    """计算归一化并开方的星系形成率比值 sqrt(SFR(z)/SFR(0))"""
    # 计算 SFR(z)
    sfr = 0.015 * (1 + z)**2.7 / (1 + ((1 + z) / 2.9)**5.6)
    # 计算 SFR(0)
    sfr0 = 0.015 * (1 + 0)**2.7 / (1 + ((1 + 0) / 2.9)**5.6)
    return np.sqrt(sfr / sfr0)

def sigma_delta_igm(z):
    """计算IGM色散量的涨落误差"""
    return 173.8 * z**0.4

def sigma_host(z):
    """宿主星系DM误差 (pc/cm³) """
    sigma_host_0 = 30  # pc/cm³
    return sigma_host_0 * SFR_ratio(z)

def calculate_dm_igm_obs(dm_obs, dm_mw_ism, z, dm_host_loc_0, dm_mw_halo=50.0):
    """计算修正后的IGM色散量 (DM_IGM_obs)"""
    dm_host = dm_host_loc_0 * SFR_ratio(z)
    return dm_obs - dm_mw_ism - dm_mw_halo - dm_host / (1 + z)

def calculate_sigma_dm_igm_obs(sigma_obs, z):
    """计算观测IGM色散量总误差 (sigma_DM_IGM_obs)"""
    sigma_mw = 30  # pc/cm³
    sigma_igm = sigma_delta_igm(z)
    sigma_host_val = 30 * SFR_ratio(z)
    sigma_host_corr = sigma_host_val / (1 + z)
    error_squared = (
        sigma_obs**2 +
        sigma_mw**2 +
        sigma_igm**2 +
        sigma_host_corr**2
    )
    return np.sqrt(error_squared)

def calculate_dm_igm_th(z, f_igm_0, alpha):
    """
    计算星系际介质的理论色散量 (DM_IGM)。
    此版本基于 Planck18 宇宙学参数，并使用以下公式:
    DM_IGM(z) = A * ∫[0→z] ( (1+z') * f_IGM(z') * fe / E(z') ) dz'
    其中:
    A = (3 * c * Ω_b0 * H0) / (8 * π * G * m_p)  (Ω_b0 是宇宙重子密度参数)
    f_IGM(z') = f_IGM,0 * (1 + α*z'/(1+z'))         (f_IGM,0 是 z=0 时的IGM重子占比)
    fe = 7/8.                                  (氦电离修正因子，近似电子数与重子数之比)
    E(z') = H(z')/H0                           (归一化哈勃参数)
    """
    # 定义物理常数 A (预计算因子)
    # A_const 的单位将由 astropy.units 自动处理
    A_const = (3 * const_astr.c * P.Ob0 * P.H0 /
                (8 * np.pi * const_astr.G * const_astr.m_p))

    fe = 7./8.  # 电子与重子的比例因子 (考虑了氦的贡献)

    def dm_igm_integrand_local(z_prime, alpha):
        """DM_IGM 计算的局部被积函数（按新公式）"""
        e_z_prime = P.efunc(z_prime)
        # 使用新给定公式 (1 + z' + α z')
        return (1 + z_prime + alpha * z_prime) * fe / e_z_prime

    # 处理 z 是标量或数组的情况
    if np.isscalar(z):
        if z < 0:
            # DM_IGM 对于 z < 0 没有物理意义，或者根据惯例返回0
            return 0.0
        if z == 0:  # 从0到0的积分为0
            return 0.0

        integral_val, _ = integrate.quad(dm_igm_integrand_local, 0, z, args=(alpha,))
        # 将 f_IGM,0 乘到积分结果外部
        dm_igm_value_with_units = A_const * f_igm_0 * integral_val
        return dm_igm_value_with_units.to(u.pc / u.cm**3).value
    else:
        #确保输入 z 是 numpy 数组以便进行矢量化操作和索引
        z_array = np.asarray(z, dtype=float)
        results = np.empty_like(z_array, dtype=float)

        for i, z_i in enumerate(z_array):
            if z_i < 0:
                results[i] = 0.0
                continue
            if z_i == 0:
                results[i] = 0.0
                continue

            integral_val, _ = integrate.quad(dm_igm_integrand_local, 0, z_i, args=(alpha,))
            # 将 f_IGM,0 乘到积分结果外部
            dm_igm_value_with_units = A_const * f_igm_0 * integral_val
            results[i] = dm_igm_value_with_units.to(u.pc / u.cm**3).value
        return results



# 2. 加载数据
def load_data():
    """读取数据并处理单位信息"""
    # 使用astropy.Table读取CSV文件
    table = Table.read('Select localized FRBs DM2.csv')
    
    # 定义单位
    dm_unit = u.pc / (u.cm**3)  # DM单位
    dist_unit = u.Gpc  # 距离单位
    
    # 添加单位信息到相关列
    table['DM_obs'] = table['DM_obs'] * dm_unit
    table['sigma_DM_obs'] = table['sigma_DM_obs'] * dm_unit
    table['DM_MW_ISM'] = table['DM_MW_ISM'] * dm_unit
    table['dL'] = table['dL'] * dist_unit
    table['dL_error'] = table['dL_error'] * dist_unit
    
    # 提取数据并确保正确的单位
    z = table['z'].value  # 红移无量纲
    dm_obs = table['DM_obs'].value  # 转换为纯数值
    sigma_dm_obs = table['sigma_DM_obs'].value
    dm_mw_ism = table['DM_MW_ISM'].value
    dL_obs = table['dL'].to(u.Mpc).value  # 转换为Mpc
    sigma_dL_obs = table['dL_error'].to(u.Mpc).value  # 转换为Mpc
    
    return z, dm_obs, sigma_dm_obs, dm_mw_ism, dL_obs, sigma_dL_obs


# 3. 定义模型
# 4. 定义先验
def log_prior(theta):
    f_IGM_0, alpha, DM_host_loc_0 = theta
    # 使用用户指定的先验边界
    if 0.0 < f_IGM_0 < 1.0 and -2.0 < alpha < 2.0 and 0.0 < DM_host_loc_0 < 200.0:
        return 0.0
    return -np.inf

# 5. 定义后验概率

def main():
    # 加载观测数据
    z, dm_obs, sigma_dm_obs, dm_mw_ism, dL_obs, sigma_dL_obs = load_data()
    # MCMC 参数设置
    ndim = 3
    nwalkers = 45
    # 使用先验区间均匀初始化 walker
    pos = np.vstack([
        np.random.uniform(0.0, 1.0, size=nwalkers),    # f_IGM_0 ∈ [0,1]
        np.random.uniform(-2.0, 2.0, size=nwalkers),   # alpha ∈ [-2,2]
        np.random.uniform(0.0, 200.0, size=nwalkers)   # DM_host_loc_0 ∈ [0,200]
    ]).T

    # 定义针对R=dL/DM_IGM的似然函数
    def log_prob(theta):
        f_igm_0, alpha, dm_host_loc_0 = theta
        # 检查先验
        if not (0.0 < f_igm_0 < 1.0 and -2.0 < alpha < 2.0 and 0.0 < dm_host_loc_0 < 200.0):
            return -np.inf
        # 计算修正后的IGM观测DM及误差
        dm_igm_obs = calculate_dm_igm_obs(dm_obs, dm_mw_ism, z, dm_host_loc_0)
        sigma_dm_igm = calculate_sigma_dm_igm_obs(sigma_dm_obs, z)
        # 计算观测R及其误差
        R_obs = dL_obs / dm_igm_obs
        sigma_R = np.sqrt((sigma_dL_obs / dm_igm_obs)**2 + (dL_obs * sigma_dm_igm / dm_igm_obs**2)**2)
        # 理论值计算
        dm_igm_th = calculate_dm_igm_th(z, f_igm_0, alpha)
        dL_th = P.luminosity_distance(z).to(u.Mpc).value
        R_th = dL_th / dm_igm_th
        # 完整高斯似然，包括归一化项
        return -0.5 * np.sum(((R_obs - R_th) / sigma_R)**2 )

    # 运行 emcee 采样
    sampler = emcee.EnsembleSampler(nwalkers, ndim, log_prob)
    sampler.run_mcmc(pos, 1000, progress=True)

    # 扔掉 burn-in，展平链
    flat_samples = sampler.get_chain(discard=100, thin=10, flat=True)
    # 计算最佳拟合参数(中位数)
    best_fit = np.median(flat_samples, axis=0)

    # 绘制 Corner 图
    labels = ["f_IGM_0", "alpha", "DM_host_loc_0"]
    fig = corner.corner(flat_samples, labels=labels, truths=best_fit, color='k', quantiles=[0.16, 0.5, 0.84], show_titles=True, title_kwargs={"fontsize":12})
    corner_axes = np.array(fig.get_axes()).reshape(ndim, ndim) # Use ndim
    for ax in np.diag(corner_axes):
        ax.spines['top'].set_visible(True)
        ax.spines['left'].set_visible(True)
        ax.spines['right'].set_visible(True)
        ax.xaxis.set_ticks_position('bottom')
        ax.yaxis.set_ticks_position('none')
    fig.savefig("corner_plot.png")

    # 绘制链图
    fig2, axes = plt.subplots(ndim, figsize=(10, 7), sharex=True)
    samples = sampler.get_chain()
    for i in range(ndim):
        ax = axes[i]
        ax.plot(samples[:, :, i], color="k", alpha=0.3)
        ax.set_ylabel(labels[i])
    axes[-1].set_xlabel("Step number")
    fig2.savefig("chain_plot.png")
    plt.show()

if __name__ == '__main__':
    main() 


