#!/usr/bin/env python3
"""
测试脚本：验证FRB宇宙学代码中关键函数的正确性
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import lognorm
from astropy.cosmology import Planck18 as P
from astropy import units as u
from astropy import constants as con
import warnings

# 导入主代码中的函数
from test import (
    calculate_dm_igm_obs, calculate_sigma_dm_igm_obs,
    calculate_dm_igm_th, sigma_delta_igm, load_data
)

def test_lognormal_distribution():
    """测试对数正态分布的期望值和方差计算"""
    print("=== 测试对数正态分布计算 ===")
    
    # 测试参数
    e_mu = 100.0  # exp(μ)
    sigma_host = 1.0
    
    # 理论值
    mu = np.log(e_mu)
    expected_mean = np.exp(mu + sigma_host**2 / 2)
    expected_var = (np.exp(sigma_host**2) - 1) * np.exp(2*mu + sigma_host**2)
    
    # 我们代码中的计算
    code_mean = e_mu * np.exp(sigma_host**2 / 2)
    code_var = (np.exp(sigma_host**2) - 1) * e_mu**2 * np.exp(sigma_host**2)
    
    print(f"理论期望值: {expected_mean:.4f}")
    print(f"代码期望值: {code_mean:.4f}")
    print(f"差异: {abs(expected_mean - code_mean):.6f}")
    
    print(f"理论方差: {expected_var:.4f}")
    print(f"代码方差: {code_var:.4f}")
    print(f"差异: {abs(expected_var - code_var):.6f}")
    
    # 蒙特卡洛验证
    samples = lognorm(s=sigma_host, scale=e_mu).rvs(size=100000)
    mc_mean = np.mean(samples)
    mc_var = np.var(samples)
    
    print(f"蒙特卡洛期望值: {mc_mean:.4f}")
    print(f"蒙特卡洛方差: {mc_var:.4f}")
    
    assert abs(expected_mean - code_mean) < 1e-10, "期望值计算错误"
    assert abs(expected_var - code_var) < 1e-10, "方差计算错误"
    print("✓ 对数正态分布计算正确")

def test_dm_igm_calculation():
    """测试DM_IGM理论值计算"""
    print("\n=== 测试DM_IGM理论值计算 ===")
    
    # 测试参数
    z_test = np.array([0.1, 0.3, 0.5, 1.0])
    f_igm_0 = 0.8
    alpha = 0.0
    
    dm_igm_values = []
    for z in z_test:
        dm_igm = calculate_dm_igm_th(z, f_igm_0, alpha)
        dm_igm_values.append(dm_igm)
        print(f"z={z:.1f}: DM_IGM_th = {dm_igm:.1f} pc/cm³")
    
    # 检查单调性（应该随红移增加）
    dm_igm_values = np.array(dm_igm_values)
    assert np.all(np.diff(dm_igm_values) > 0), "DM_IGM应该随红移单调增加"
    
    # 检查数值合理性
    assert np.all(dm_igm_values > 0), "DM_IGM应该为正值"
    assert np.all(dm_igm_values < 5000), "DM_IGM值过大，可能有问题"
    
    print("✓ DM_IGM理论值计算合理")

def test_error_propagation():
    """测试误差传播计算"""
    print("\n=== 测试误差传播 ===")
    
    # 模拟数据
    z = np.array([0.1, 0.3, 0.5])
    sigma_obs = np.array([1.0, 2.0, 3.0])
    e_mu = 100.0
    sigma_host = 1.0
    
    sigma_total = calculate_sigma_dm_igm_obs(sigma_obs, z, e_mu, sigma_host)
    
    print(f"输入观测误差: {sigma_obs}")
    print(f"总误差: {sigma_total}")
    
    # 检查误差是否合理增加
    assert np.all(sigma_total > sigma_obs), "总误差应该大于观测误差"
    assert np.all(sigma_total < 500), "误差值过大"  # 放宽限制，因为IGM贡献较大
    
    print("✓ 误差传播计算合理")

def test_data_loading():
    """测试数据加载和筛选"""
    print("\n=== 测试数据加载 ===")
    
    try:
        z, dm_obs, sigma_dm_obs, dm_mw_ism, dL_obs, sigma_dL_obs = load_data()
        
        print(f"加载数据点数: {len(z)}")
        print(f"红移范围: {z.min():.4f} - {z.max():.4f}")
        print(f"DM范围: {dm_obs.min():.1f} - {dm_obs.max():.1f}")
        
        # 基本检查
        assert len(z) > 0, "没有数据点"
        assert np.all(z > 0), "红移应该为正"
        assert np.all(dm_obs > dm_mw_ism), "观测DM应该大于银河系贡献"
        assert np.all(sigma_dm_obs > 0), "误差应该为正"
        
        print("✓ 数据加载成功")
        
    except FileNotFoundError:
        print("⚠ 数据文件未找到，跳过数据加载测试")
    except Exception as e:
        print(f"✗ 数据加载失败: {e}")

def test_physical_constraints():
    """测试物理约束"""
    print("\n=== 测试物理约束 ===")
    
    # 测试极端参数
    z = np.array([0.1, 0.5, 1.0])
    dm_obs = np.array([200, 400, 600])
    dm_mw_ism = np.array([50, 80, 100])
    
    # 测试极端宿主DM参数
    e_mu_extreme = 500.0  # 很大的宿主DM
    sigma_host_extreme = 2.0
    
    dm_igm_obs = calculate_dm_igm_obs(dm_obs, dm_mw_ism, z, e_mu_extreme, sigma_host_extreme)
    
    print(f"极端参数下的DM_IGM_obs: {dm_igm_obs}")
    
    # 检查是否有负值
    negative_count = np.sum(dm_igm_obs <= 0)
    if negative_count > 0:
        print(f"⚠ 有{negative_count}个负的DM_IGM_obs值")
    else:
        print("✓ 所有DM_IGM_obs值为正")

def plot_dm_z_relation():
    """绘制DM-z关系图"""
    print("\n=== 绘制DM-z关系 ===")
    
    z_range = np.linspace(0.01, 1.5, 100)
    
    # 不同参数的理论曲线
    params = [
        (0.8, 0.0, "f_IGM=0.8, α=0"),
        (0.8, 0.5, "f_IGM=0.8, α=0.5"),
        (0.8, -0.5, "f_IGM=0.8, α=-0.5"),
        (0.6, 0.0, "f_IGM=0.6, α=0"),
    ]
    
    plt.figure(figsize=(10, 6))
    
    for f_igm_0, alpha, label in params:
        dm_igm_th = []
        for z in z_range:
            try:
                dm = calculate_dm_igm_th(z, f_igm_0, alpha)
                dm_igm_th.append(dm)
            except:
                dm_igm_th.append(np.nan)
        
        plt.plot(z_range, dm_igm_th, label=label, linewidth=2)
    
    plt.xlabel('Redshift z')
    plt.ylabel('DM_IGM_th (pc/cm³)')
    plt.title('Theoretical DM_IGM vs Redshift')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xlim(0, 1.5)
    plt.ylim(0, 2000)
    
    plt.tight_layout()
    plt.savefig('dm_z_relation.png', dpi=300, bbox_inches='tight')
    print("✓ DM-z关系图已保存为 dm_z_relation.png")

def main():
    """运行所有测试"""
    print("开始函数验证测试...\n")
    
    test_lognormal_distribution()
    test_dm_igm_calculation()
    test_error_propagation()
    test_data_loading()
    test_physical_constraints()
    plot_dm_z_relation()
    
    print("\n=== 测试完成 ===")
    print("如果没有断言错误，说明主要函数工作正常")

if __name__ == '__main__':
    main()
