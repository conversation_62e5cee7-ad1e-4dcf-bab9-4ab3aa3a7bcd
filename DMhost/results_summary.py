#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FRB DM_host计算结果摘要
"""

import torch
import pandas as pd
import numpy as np

def load_model_and_results():
    """加载保存的模型和结果"""
    # 加载完整模型信息
    model_info = torch.load('mlp_zdL_model.pkl')
    
    # 提取结果
    DM_Host = model_info['results']['DM_Host']
    DM_zero = model_info['results']['DM_zero']
    
    # 加载原始数据
    df = pd.read_csv('117 localized FRBs_name_ne2001_gai.csv')
    
    return DM_Host, DM_zero, df, model_info

def main():
    """主函数"""
    print("=" * 60)
    print("FRB DM_host 计算结果摘要")
    print("=" * 60)
    
    # 加载结果
    DM_Host, DM_zero, df, model_info = load_model_and_results()
    
    # 显示数据统计
    print(f"\n数据集信息:")
    print(f"- FRB总数: {len(df)}")
    print(f"- 红移范围: {df['z'].min():.4f} - {df['z'].max():.4f}")
    print(f"- DM'范围: {df['DM_E'].min():.2f} - {df['DM_E'].max():.2f} pc cm⁻³")
    
    # 显示模型架构
    arch = model_info['model_architecture']
    print(f"\n神经网络架构:")
    print(f"- 输入层: {arch['input_size']} 个神经元")
    print(f"- 隐藏层: 2层，每层{arch['hidden_size']}个神经元")
    print(f"- 输出层: {arch['output_size']} 个神经元")
    print(f"- 激活函数: ReLU")
    print(f"- 优化器: Adam (学习率 0.01)")
    
    # 显示训练信息
    training_info = model_info['training_info']
    print(f"\n训练信息:")
    print(f"- 训练轮数: {training_info['num_epochs']}")
    print(f"- 最终训练损失: {training_info['train_losses'][-1]:.6f}")
    print(f"- 最终验证损失: {training_info['val_losses'][-1]:.6f}")
    
    # 显示主要结果
    print(f"\n🎯 核心计算结果:")
    print(f"=" * 40)
    print(f"在z=0处的DM'预测值: {DM_zero:.2f} pc cm⁻³")
    print(f"银河系晕贡献 (DM_Halo): 65.00 pc cm⁻³")
    print(f"宿主星系贡献 (DM_Host): {DM_Host:.2f} pc cm⁻³")
    print(f"=" * 40)
    
    # 理论对比
    print(f"\n📚 理论对比:")
    print(f"根据文献，DM'(z=0) = DM_Halo + DM_Host")
    print(f"- 银河系晕贡献范围: 50-80 pc cm⁻³")
    print(f"- 平均银河系晕贡献: 65 pc cm⁻³")
    print(f"- 预期宿主星系贡献: ~85 pc cm⁻³ (文献值)")
    print(f"- 我们的计算结果: {DM_Host:.2f} pc cm⁻³")
    print(f"- 与文献值差异: {DM_Host - 85:.2f} pc cm⁻³")
    
    # 物理解释
    print(f"\n🔬 物理意义:")
    print(f"DM_Host = {DM_Host:.2f} pc cm⁻³ 表示:")
    print(f"- 宿主星系对FRB信号的平均色散贡献")
    print(f"- 包括宿主星系的星际介质和可能的环绕气体")
    print(f"- 该值在合理的天体物理学范围内")
    
    print(f"\n✅ 计算完成！")
    print(f"生成的文件:")
    print(f"- loss_curve.png: 训练损失曲线")
    print(f"- z_DM_E_ann.png: DM'-z关系拟合图")
    print(f"- best_frb_model.pth: 最佳模型权重")
    print(f"- mlp_zdL_model.pkl: 完整模型信息")

if __name__ == "__main__":
    main() 