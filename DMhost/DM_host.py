#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FRB DM'-z关系重建及DM_host计算程序
基于人工神经网络的快速射电暴色散量-红移关系建模
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.optimize import curve_fit
import warnings
import random

# 添加随机种子以确保训练结果可复现
random.seed(42)
np.random.seed(42)
torch.manual_seed(42)
torch.cuda.manual_seed_all(42)
torch.backends.cudnn.deterministic = True
torch.backends.cudnn.benchmark = False

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class FRBNet(nn.Module):
    """
    FRB神经网络模型
    架构：输入层 + 两个隐藏层（每层100个神经元）+ 输出层
    """
    def __init__(self, input_size=1, hidden_size=100, output_size=1):
        super(FRBNet, self).__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        self.fc3 = nn.Linear(hidden_size, output_size)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.1)  # 添加dropout防止过拟合
        
    def forward(self, x):
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        return x

class WeightedMSELoss(nn.Module):
    """
    加权均方误差损失函数
    χ²_MSE = (1/N) * Σ(1/σ²) * (DM_pred - DM_true)²
    """
    def __init__(self):
        super(WeightedMSELoss, self).__init__()
        
    def forward(self, predictions, targets, weights):
        # weights = 1/σ²
        weighted_mse = torch.mean(weights * (predictions - targets)**2)
        return weighted_mse

def load_frb_data(file_path):
    """
    加载FRB数据
    
    参数:
    file_path: CSV文件路径
    
    返回:
    z: 红移数组
    DM_E: DM'数组 (DM - DM_MW)
    sigma_total: 总不确定性数组
    df: 原始数据DataFrame
    """
    # --- 数据加载 ---
    print("正在加载FRB数据...")
    df = pd.read_csv(file_path)
    print(f"成功加载 {len(df)} 个FRB数据点")
    
    # --- 数据提取与处理 ---
    z = df['z'].values
    DM_E = df['DM_E'].values  # DM' = DM - DM_MW
    sigma_DM_obs = df['sigma_DM_obs'].values
    
    # 计算总不确定性：σ² = σ_obs² + σ_MW²
    sigma_MW = 30.0  # pc cm⁻³，银河系建模不确定性
    sigma_total = np.sqrt(sigma_DM_obs**2 + sigma_MW**2)
    
    # --- 打印统计信息 ---
    print(f"红移范围: {z.min():.4f} - {z.max():.4f}")
    print(f"DM'范围: {DM_E.min():.2f} - {DM_E.max():.2f} pc cm⁻³")
    
    return z, DM_E, sigma_total, df

def create_data_loaders(z, DM_E, sigma_total, test_size=0.2, batch_size=32):
    """
    创建训练和验证数据加载器
    
    参数:
    z: 红移数组
    DM_E: DM'数组
    sigma_total: 总不确定性数组
    test_size: 验证集比例
    batch_size: 批处理大小
    
    返回:
    train_loader: 训练数据加载器
    val_loader: 验证数据加载器
    """
    # 计算权重 (1/σ²)
    weights = 1.0 / (sigma_total**2)
    
    # 转换为PyTorch张量
    z_tensor = torch.FloatTensor(z.reshape(-1, 1))
    DM_E_tensor = torch.FloatTensor(DM_E.reshape(-1, 1))
    weights_tensor = torch.FloatTensor(weights.reshape(-1, 1))
    
    # 数据分割（80:20）
    indices = np.arange(len(z))
    train_idx, val_idx = train_test_split(indices, test_size=test_size, random_state=42)
    
    print(f"训练集大小: {len(train_idx)}")
    print(f"验证集大小: {len(val_idx)}")
    
    # 创建数据集
    train_dataset = torch.utils.data.TensorDataset(
        z_tensor[train_idx], DM_E_tensor[train_idx], weights_tensor[train_idx]
    )
    val_dataset = torch.utils.data.TensorDataset(
        z_tensor[val_idx], DM_E_tensor[val_idx], weights_tensor[val_idx]
    )
    
    # 创建数据加载器
    train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = torch.utils.data.DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    
    return train_loader, val_loader

def train_model(model, train_loader, val_loader, num_epochs=1000, learning_rate=0.01):
    """
    训练神经网络模型
    
    参数:
    model: 神经网络模型
    train_loader: 训练数据加载器
    val_loader: 验证数据加载器
    num_epochs: 训练轮数
    learning_rate: 学习率
    
    返回:
    train_losses: 训练损失历史
    val_losses: 验证损失历史
    """
    print("开始训练神经网络...")
    
    criterion = WeightedMSELoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', patience=100, factor=0.5)
    
    train_losses = []
    val_losses = []
    
    best_val_loss = float('inf')
    patience_counter = 0
    patience = 200  # 早停机制
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_batches = 0
        
        for batch_z, batch_dm, batch_weights in train_loader:
            optimizer.zero_grad()
            
            outputs = model(batch_z)
            loss = criterion(outputs, batch_dm, batch_weights)
            
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_batches += 1
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_batches = 0
        
        with torch.no_grad():
            for batch_z, batch_dm, batch_weights in val_loader:
                outputs = model(batch_z)
                loss = criterion(outputs, batch_dm, batch_weights)
                val_loss += loss.item()
                val_batches += 1
        
        avg_train_loss = train_loss / train_batches
        avg_val_loss = val_loss / val_batches
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        
        # 学习率调度
        scheduler.step(avg_val_loss)
        
        # 早停机制
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            patience_counter = 0
            # 保存最佳模型
            torch.save(model.state_dict(), 'best_frb_model.pth')
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print(f"在第 {epoch+1} 轮触发早停机制")
                break
        
        if epoch % 100 == 0:
            current_lr = optimizer.param_groups[0]['lr']
            print(f'Epoch [{epoch+1}/{num_epochs}], Train Loss: {avg_train_loss:.6f}, '
                  f'Val Loss: {avg_val_loss:.6f}, LR: {current_lr:.6f}')
    
    # 加载最佳模型
    model.load_state_dict(torch.load('best_frb_model.pth'))
    print("训练完成！")
    
    return train_losses, val_losses

def plot_training_history(train_losses, val_losses):
    """
    绘制训练历史曲线
    """
    plt.figure(figsize=(12, 5))
    
    plt.subplot(1, 2, 1)
    plt.plot(train_losses, label='Training Loss', color='purple', linewidth=2)
    plt.plot(val_losses, label='Validation Loss', color='green', linewidth=2)
    plt.xlabel('Epoch')
    plt.ylabel('Weighted MSE Loss')
    plt.title('训练和验证损失')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.yscale('log')
    
    plt.subplot(1, 2, 2)
    # 绘制后半部分的损失，看收敛情况
    start_idx = len(train_losses) // 2
    plt.plot(train_losses[start_idx:], label='Training Loss', color='purple', linewidth=2)
    plt.plot(val_losses[start_idx:], label='Validation Loss', color='green', linewidth=2)
    plt.xlabel('Epoch')
    plt.ylabel('Weighted MSE Loss')
    plt.title('训练后期损失变化')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('DMhost/loss_curve.png', dpi=300, bbox_inches='tight')
    plt.show()

def calculate_dm_host(model, verbose=True):
    """
    计算DM_host
    
    根据文本：
    - 在z=0处，DM'(z=0) = DM_Halo + DM_Host
    - DM_Halo = 65 pc cm⁻³ (平均值)
    - 因此 DM_Host = DM'(z=0) - DM_Halo
    """
    model.eval()
    with torch.no_grad():
        z_zero = torch.FloatTensor([[0.0]])
        DM_zero = model(z_zero).item()
    
    # 银河系晕贡献（文献值）
    DM_Halo = 65.0  # pc cm⁻³
    
    # 计算宿主星系贡献
    DM_Host = DM_zero - DM_Halo
    
    if verbose:
        print("\n" + "="*50)
        print("DM_host 计算结果")
        print("="*50)
        print(f"在z=0处的DM'预测值: {DM_zero:.2f} pc cm⁻³")
        print(f"银河系晕贡献 (DM_Halo): {DM_Halo:.2f} pc cm⁻³")
        print(f"宿主星系贡献 (DM_Host): {DM_Host:.2f} pc cm⁻³")
        print("="*50)
    
    return DM_Host, DM_zero

def plot_dm_z_relation(model, z, DM_E, sigma_total):
    """
    绘制DM'-z关系图
    """
    print("正在生成DM'-z关系图...")
    
    # Bayesian uncertainty estimation using Monte Carlo dropout
    z_pred = np.linspace(0, 1.6, 1000)
    z_pred_tensor = torch.FloatTensor(z_pred.reshape(-1, 1))
    
    # 使用蒙特卡洛Dropout进行贝叶斯不确定性估计
    mc_samples = 100
    predictions = []
    
    model.train()  # 激活dropout
    with torch.no_grad():
        for _ in range(mc_samples):
            pred = model(z_pred_tensor).numpy().flatten()
            predictions.append(pred)
    
    predictions = np.array(predictions)
    DM_pred = predictions.mean(axis=0)  # 预测均值
    uncertainty_band = predictions.std(axis=0)  # 1σ不确定性带
    
    # 绘图
    plt.figure(figsize=(12, 8))
    
    # 绘制观测数据点
    plt.errorbar(z, DM_E, yerr=sigma_total, fmt='o', color='blue', alpha=0.7, 
                 label='Data points', markersize=4, capsize=2)
    
    # 绘制ML预测曲线
    plt.plot(z_pred, DM_pred, 'r-', linewidth=2, label='ML predicted curve')
    
    # 添加1σ不确定性区域
    plt.fill_between(z_pred, DM_pred - uncertainty_band, DM_pred + uncertainty_band, 
                     alpha=0.3, color='red', label='1σ uncertainty')
    
    # 设置图形属性
    plt.xlabel('$z_s$', fontsize=14)
    plt.ylabel("$\\mathrm{DM}' = \\mathrm{DM} - \\mathrm{DM}_{\\mathrm{MW}}$ (pc cm$^{-3}$)", fontsize=14)
    plt.title('Observed and predicted DM\' - $z_s$ relation', fontsize=16)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.xlim(0, 1.6)
    plt.ylim(0, 1500)
    plt.tight_layout()
    plt.savefig('DMhost/z_DM_E_ann.png', dpi=300, bbox_inches='tight')
    plt.show()

def model_statistics(model, z, DM_E, sigma_total):
    """
    计算模型统计信息
    """
    model.eval()
    with torch.no_grad():
        z_tensor = torch.FloatTensor(z.reshape(-1, 1))
        DM_pred = model(z_tensor).numpy().flatten()
    
    # 计算残差
    residuals = DM_E - DM_pred
    
    # 计算统计量
    mse = np.mean(residuals**2)
    rmse = np.sqrt(mse)
    mae = np.mean(np.abs(residuals))
    
    # 计算加权统计量
    weights = 1.0 / (sigma_total**2)
    weighted_mse = np.mean(weights * residuals**2)
    
    # 计算R²
    ss_res = np.sum(residuals**2)
    ss_tot = np.sum((DM_E - np.mean(DM_E))**2)
    r_squared = 1 - (ss_res / ss_tot)
    
    print("\n" + "="*50)
    print("模型性能统计")
    print("="*50)
    print(f"均方误差 (MSE): {mse:.2f}")
    print(f"均方根误差 (RMSE): {rmse:.2f} pc cm⁻³")
    print(f"平均绝对误差 (MAE): {mae:.2f} pc cm⁻³")
    print(f"加权均方误差 (Weighted MSE): {weighted_mse:.6f}")
    print(f"决定系数 (R²): {r_squared:.4f}")
    print("="*50)

def main():
    """
    主函数
    """
    print("FRB DM'-z关系重建及DM_host计算程序")
    print("="*60)
    
    # 1. 加载数据
    z, DM_E, sigma_total, df = load_frb_data('DMhost/117 localized FRBs_name_ne2001_gai.csv')
    
    # 2. 创建数据加载器
    train_loader, val_loader = create_data_loaders(z, DM_E, sigma_total)
    
    # 3. 创建模型
    model = FRBNet(input_size=1, hidden_size=100, output_size=1)
    print(f"\n模型架构:")
    print(f"输入层: 1 个神经元")
    print(f"隐藏层1: 100 个神经元 (ReLU)")
    print(f"隐藏层2: 100 个神经元 (ReLU)")
    print(f"输出层: 1 个神经元")
    print(f"总参数数量: {sum(p.numel() for p in model.parameters())}")
    
    # 4. 训练模型
    train_losses, val_losses = train_model(model, train_loader, val_loader, 
                                          num_epochs=1000, learning_rate=0.01)
    
    # 5. 绘制训练历史
    plot_training_history(train_losses, val_losses)
    
    # 6. 计算DM_host
    DM_Host, DM_zero = calculate_dm_host(model)
    
    # 7. 绘制DM'-z关系图
    plot_dm_z_relation(model, z, DM_E, sigma_total)
    
    # 8. 计算模型统计信息
    model_statistics(model, z, DM_E, sigma_total)
    
    # 9. 保存模型和相关信息
    torch.save({
        'model_state_dict': model.state_dict(),
        'model_architecture': {
            'input_size': 1,
            'hidden_size': 100,
            'output_size': 1
        },
        'training_info': {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'num_epochs': len(train_losses)
        },
        'results': {
            'DM_Host': DM_Host,
            'DM_zero': DM_zero
        }
    }, 'mlp_zdL_model.pkl')
    
    print(f"\n程序执行完成！")
    print(f"已保存以下文件：")
    print(f"- best_frb_model.pth: 最佳模型权重")
    print(f"- mlp_zdL_model.pkl: 完整模型信息")
    print(f"- loss_curve.png: 训练损失曲线")
    print(f"- z_DM_E_ann.png: DM'-z关系图")
    
    return model, DM_Host

if __name__ == "__main__":
    model, DM_Host = main()
