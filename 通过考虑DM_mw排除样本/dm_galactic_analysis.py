#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from astropy.coordinates import SkyCoord
import astropy.units as u

# Read the CSV file
df = pd.read_csv('Select localized FRBs DM_name.csv')

# Clean the data by removing trailing spaces
for col in df.columns:
    if df[col].dtype == 'object':
        df[col] = df[col].str.strip() if hasattr(df[col], 'str') else df[col]

# Convert numeric columns to float
numeric_cols = ['RA(deg)', 'DEC(deg)', 'DM_obs(pc cm^-3)', 'Delta_DM(pc cm^-3)',
               'z_host', 'DM_MW_NE2001(pc/cm^3)', 'DM_MW_YMW16(pc/cm^3)']
for col in numeric_cols:
    df[col] = pd.to_numeric(df[col], errors='coerce')

# Drop rows with missing values in critical columns
df = df.dropna(subset=['RA(deg)', 'DEC(deg)', 'DM_MW_NE2001(pc/cm^3)', 'DM_MW_YMW16(pc/cm^3)'])

# Convert RA and DEC to Galactic coordinates
coords = SkyCoord(ra=df['RA(deg)'].values*u.degree,
                  dec=df['DEC(deg)'].values*u.degree,
                  frame='icrs')
galactic = coords.galactic
df['GB'] = galactic.b.degree  # Galactic latitude in degrees
df['abs_GB'] = np.abs(df['GB'])  # Absolute value of Galactic latitude

# Calculate the DM difference between NE2001 and YMW16 models (NE2001 - YMW16)
df['DM_diff'] = df['DM_MW_NE2001(pc/cm^3)'] - df['DM_MW_YMW16(pc/cm^3)']

# Create a new column to categorize YMW16 values compared to NE2001
df['YMW16_vs_NE2001'] = np.where(df['DM_MW_YMW16(pc/cm^3)'] > df['DM_MW_NE2001(pc/cm^3)'],
                                 'larger', 'smaller')

# Identify outliers for different cases
# Case 1: Exclude FRBs with low DMMW in YMW16 model (ΔDMMW > 50 pc cm−3)
df['case1_outlier'] = df['DM_diff'] > 50

# Case 2: Exclude FRBs with significant differences in DMMW from the two models (|ΔDMMW| > 50 pc cm−3)
df['case2_outlier'] = np.abs(df['DM_diff']) > 50

# Case 3: Limit to FRBs with DMMW values less than 100 pc cm−3 in both models
df['case3_include'] = (df['DM_MW_NE2001(pc/cm^3)'] < 100) & (df['DM_MW_YMW16(pc/cm^3)'] < 100)
df['case3_outlier'] = ~df['case3_include']

# Print information about outliers
case1_outliers = df[df['case1_outlier']]
case2_outliers = df[df['case2_outlier']]
case2_only_outliers = df[df['case2_outlier'] & ~df['case1_outlier']]  # Case 2 excluding Case 1
case3_outliers = df[df['case3_outlier']]
case3_only_outliers = df[df['case3_outlier'] & ~df['case2_outlier']]  # Case 3 excluding Case 2

# Create a detailed output format for outliers
def format_outlier_details(outliers, case_name):
    output = f"\n{case_name} Outliers: {len(outliers)} FRBs\n"
    if not outliers.empty:
        # Create a formatted table header
        output += "\n{:<15} {:<10} {:<10} {:<15} {:<15} {:<15}\n".format(
            "FRB Name", "Abs GB", "GB", "NE2001 DM", "YMW16 DM", "ΔDM (NE2001-YMW16)")
        output += "-" * 80 + "\n"

        # Sort by absolute Galactic latitude
        for _, row in outliers.sort_values('abs_GB').iterrows():
            output += "{:<15} {:<10.2f} {:<10.2f} {:<15.2f} {:<15.2f} {:<15.2f}\n".format(
                row['TNSname'].replace('FRB', ''),
                row['abs_GB'],
                row['GB'],
                row['DM_MW_NE2001(pc/cm^3)'],
                row['DM_MW_YMW16(pc/cm^3)'],
                row['DM_diff'])
    return output

# Generate detailed information for each case
case1_output = format_outlier_details(case1_outliers, "Case 1")
case2_output = format_outlier_details(case2_only_outliers, "Case 2 (excluding Case 1)")
case3_output = format_outlier_details(case3_only_outliers, "Case 3 (excluding Cases 1 & 2)")

# Generate summary counts
summary_output = "\nSummary of Outliers:\n"
summary_output += f"Case 1 - FRBs with low DMMW in YMW16 (ΔDMMW > 50 pc cm−3): {len(case1_outliers)} FRBs\n"
summary_output += f"Case 2 - FRBs with significant differences (|ΔDMMW| > 50 pc cm−3): {len(case2_outliers)} FRBs\n"
summary_output += f"Case 3 - FRBs with DMMW >= 100 pc cm−3 in either model: {len(case3_outliers)} FRBs\n"

# Print the outputs
print(case1_output)
print(case2_output)
print(case3_output)
print(summary_output)

# Sort by absolute Galactic latitude for better visualization
df = df.sort_values('abs_GB')

# Set the style for the plot
plt.style.use('default')

# Create the plot with a better aspect ratio
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), sharex=True,
                               gridspec_kw={'height_ratios': [2, 1], 'hspace': 0.05})

# Upper panel: DMMW for FRBs using NE2001 and YMW16 models
# Plot YMW16 values with different colors based on comparison with NE2001
larger_mask = df['YMW16_vs_NE2001'] == 'larger'
smaller_mask = df['YMW16_vs_NE2001'] == 'smaller'

# Sort data by absolute Galactic latitude for line plot
df_sorted = df.sort_values('abs_GB')

# Plot NE2001 values as a connected line with points
ax1.plot(df_sorted['abs_GB'], df_sorted['DM_MW_NE2001(pc/cm^3)'], '-', color='black',
         label='NE2001', marker='o', markersize=4, linewidth=1)

# Plot YMW16 values with different colors based on comparison with NE2001
ax1.scatter(df.loc[smaller_mask, 'abs_GB'], df.loc[smaller_mask, 'DM_MW_YMW16(pc/cm^3)'],
            color='#6495ED', marker='v', s=40, label='YMW16 (< NE2001)')
ax1.scatter(df.loc[larger_mask, 'abs_GB'], df.loc[larger_mask, 'DM_MW_YMW16(pc/cm^3)'],
            color='#FFA500', marker='^', s=40, label='YMW16 (> NE2001)')

# Mark outliers for Case 1 (ΔDMMW > 50 pc cm−3)
case1_mask = df['case1_outlier']
if case1_mask.any():
    # Mark Case 1 outliers with red X
    ax1.scatter(df.loc[case1_mask, 'abs_GB'], df.loc[case1_mask, 'DM_MW_NE2001(pc/cm^3)'],
                color='red', marker='x', s=100, linewidth=2, label='Case 1 Outliers')
    ax1.scatter(df.loc[case1_mask, 'abs_GB'], df.loc[case1_mask, 'DM_MW_YMW16(pc/cm^3)'],
                color='red', marker='x', s=100, linewidth=2)

# Mark outliers for Case 2 (|ΔDMMW| > 50 pc cm−3)
case2_mask = df['case2_outlier'] & ~df['case1_outlier']  # Exclude Case 1 outliers to avoid double marking
if case2_mask.any():
    # Mark Case 2 outliers with green squares
    ax1.scatter(df.loc[case2_mask, 'abs_GB'], df.loc[case2_mask, 'DM_MW_NE2001(pc/cm^3)'],
                color='green', marker='s', s=80, linewidth=2, label='Case 2 Outliers')
    ax1.scatter(df.loc[case2_mask, 'abs_GB'], df.loc[case2_mask, 'DM_MW_YMW16(pc/cm^3)'],
                color='green', marker='s', s=80, linewidth=2)

# Mark outliers for Case 3 (DMMW >= 100 pc cm−3 in either model)
case3_mask = df['case3_outlier'] & ~df['case2_outlier']  # Exclude already marked outliers
if case3_mask.any():
    # Mark Case 3 outliers with blue diamonds
    ax1.scatter(df.loc[case3_mask, 'abs_GB'], df.loc[case3_mask, 'DM_MW_NE2001(pc/cm^3)'],
                color='blue', marker='d', s=70, linewidth=2, label='Case 3 Outliers')
    ax1.scatter(df.loc[case3_mask, 'abs_GB'], df.loc[case3_mask, 'DM_MW_YMW16(pc/cm^3)'],
                color='blue', marker='d', s=70, linewidth=2)

# Lower panel: difference between DMMW predicted by NE2001 and YMW16
ax2.plot(df_sorted['abs_GB'], df_sorted['DM_diff'], '-', color='#9370DB',
         marker='o', markersize=3, linewidth=1, label='DM$_{MW}$(NE2001) $-$ DM$_{MW}$(YMW16)')
ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5, linewidth=0.5)

# Add grid lines
ax1.grid(True, linestyle='--', color='lightgray')
ax2.grid(True, linestyle='--', color='lightgray')

# Set labels and titles with formatting similar to the reference image
ax1.set_ylabel('DM$_{MW}$ (pc cm$^{-3}$)', fontsize=12)
ax1.legend(loc='upper right', fontsize=10, framealpha=0.9)

ax2.set_xlabel('$|b|$ (deg)', fontsize=12)
ax2.set_ylabel(r'$\Delta$DM$_{MW}$ (pc cm$^{-3}$)', fontsize=12)
ax2.legend(loc='upper right', fontsize=10)

# Set y-axis limits for the lower panel to match the reference image
ax2.set_ylim(-180, 180)

# Adjust y-axis limits for better visualization
ax1.set_ylim(bottom=0)
y_max = max(df['DM_MW_NE2001(pc/cm^3)'].max(), df['DM_MW_YMW16(pc/cm^3)'].max()) * 1.1
ax1.set_ylim(top=y_max)

# Set x-axis limits to match the reference image (0 to 90 degrees)
ax2.set_xlim(0, 90)

# Adjust figure size and spacing
fig.subplots_adjust(left=0.12, right=0.95, top=0.95, bottom=0.1, hspace=0.05)

# Save the figure with higher resolution
plt.savefig('DM_galactic_latitude_comparison.png', dpi=300)

# Also save as PDF for vector graphics (better for publications)
# plt.savefig('DM_galactic_latitude_comparison.pdf')

# Don't use plt.show() in non-interactive environments
# plt.show()

# Generate statistics output
stats_output = "\nSummary Statistics:\n"
stats_output += f"Total FRBs analyzed: {len(df)}\n"
stats_output += f"FRBs where YMW16 > NE2001: {sum(larger_mask)}\n"
stats_output += f"FRBs where YMW16 < NE2001: {sum(smaller_mask)}\n"
stats_output += f"Average DM difference (NE2001 - YMW16): {df['DM_diff'].mean():.2f} pc cm^-3\n"
stats_output += f"Maximum absolute DM difference: {df['DM_diff'].abs().max():.2f} pc cm^-3\n"

# Analysis for each case
analysis_output = "\nAnalysis for each case:\n"
case1_filtered = df[~df['case1_outlier']]
analysis_output += f"Case 1: After excluding {len(case1_outliers)} FRBs with ΔDMMW > 50 pc cm−3\n"
analysis_output += f"  - Average DM difference: {case1_filtered['DM_diff'].mean():.2f} pc cm^-3\n"
analysis_output += f"  - Standard deviation: {case1_filtered['DM_diff'].std():.2f} pc cm^-3\n"

case2_filtered = df[~df['case2_outlier']]
analysis_output += f"\nCase 2: After excluding {len(case2_outliers)} FRBs with |ΔDMMW| > 50 pc cm−3\n"
analysis_output += f"  - Average DM difference: {case2_filtered['DM_diff'].mean():.2f} pc cm^-3\n"
analysis_output += f"  - Standard deviation: {case2_filtered['DM_diff'].std():.2f} pc cm^-3\n"

case3_filtered = df[df['case3_include']]
analysis_output += f"\nCase 3: Limiting to {len(df[df['case3_include']])} FRBs with DMMW < 100 pc cm−3 in both models\n"
analysis_output += f"  - Average DM difference: {case3_filtered['DM_diff'].mean():.2f} pc cm^-3\n"
analysis_output += f"  - Standard deviation: {case3_filtered['DM_diff'].std():.2f} pc cm^-3\n"

# Print the statistics and analysis
print(stats_output)
print(analysis_output)

# Combine all outputs into a single text file
all_output = case1_output + case2_output + case3_output + summary_output + stats_output + analysis_output

# Save the combined output to a text file
with open('outliers_results.txt', 'w') as f:
    f.write(all_output)
print("\nSaved detailed results to outliers_results.txt")

# Save the results to a CSV file with the new columns
df.to_csv('FRB_DM_galactic_analysis.csv', index=False)

# Save FRB data to CSV files
def save_frb_data_to_csv(frb_df, filename):
    if not frb_df.empty:
        # Select relevant columns and sort by absolute Galactic latitude
        output_df = frb_df[['TNSname', 'abs_GB', 'GB', 'DM_MW_NE2001(pc/cm^3)', 'DM_MW_YMW16(pc/cm^3)', 'DM_diff']]
        output_df = output_df.sort_values('abs_GB')
        # Rename columns for better readability
        output_df = output_df.rename(columns={
            'TNSname': 'FRB_Name',
            'abs_GB': 'Abs_GB',
            'GB': 'GB',
            'DM_MW_NE2001(pc/cm^3)': 'NE2001_DM',
            'DM_MW_YMW16(pc/cm^3)': 'YMW16_DM',
            'DM_diff': 'DM_diff_NE2001_minus_YMW16'
        })
        # Save to CSV
        output_df.to_csv(filename, index=False)
        print(f"Saved {len(output_df)} FRBs to {filename}")
        return len(output_df)
    return 0

# Save each case of outliers
save_frb_data_to_csv(case1_outliers, 'case1_outliers.csv')
save_frb_data_to_csv(case2_only_outliers, 'case2_outliers.csv')
save_frb_data_to_csv(case3_only_outliers, 'case3_outliers.csv')

# Save all outliers combined
all_outliers = pd.concat([case1_outliers, case2_only_outliers, case3_only_outliers])
save_frb_data_to_csv(all_outliers, 'all_outliers.csv')

# Find FRBs that satisfy all conditions (not outliers in any case)
# These are FRBs with:
# 1. |ΔDMMW| <= 50 pc cm−3 (not Case 1 or Case 2)
# 2. DMMW < 100 pc cm−3 in both models (not Case 3)
all_good_frbs = df[~df['case1_outlier'] & ~df['case2_outlier'] & ~df['case3_outlier']]

# Save the good FRBs to CSV with our calculated parameters
good_frbs_count = save_frb_data_to_csv(all_good_frbs, 'good_frbs.csv')

# Save the good FRBs with all original parameters from the input file
# Get the list of good FRB names
good_frb_names = all_good_frbs['TNSname'].tolist()

# Read the original CSV file again to get all parameters
original_df = pd.read_csv('Select localized FRBs DM_name.csv')

# Clean the TNSname column in the original dataframe if needed
if 'TNSname' in original_df.columns:
    original_df['TNSname'] = original_df['TNSname'].str.strip() if hasattr(original_df['TNSname'], 'str') else original_df['TNSname']

# Filter the original dataframe to get only the good FRBs
good_frbs_full = original_df[original_df['TNSname'].isin(good_frb_names)]

# Add our calculated parameters to the full dataset
for frb_name in good_frb_names:
    # Find the corresponding row in our processed dataframe
    processed_row = all_good_frbs[all_good_frbs['TNSname'] == frb_name]
    if not processed_row.empty:
        # Find the index in the original dataframe
        idx = good_frbs_full[good_frbs_full['TNSname'] == frb_name].index
        if len(idx) > 0:
            # Add the calculated parameters
            good_frbs_full.loc[idx, 'GB'] = processed_row['GB'].values[0]
            good_frbs_full.loc[idx, 'abs_GB'] = processed_row['abs_GB'].values[0]
            good_frbs_full.loc[idx, 'DM_diff'] = processed_row['DM_diff'].values[0]

# Save the full parameters to CSV
good_frbs_full.to_csv('good_frbs_full_params.csv', index=False)
print(f"Saved {len(good_frbs_full)} FRBs with full parameters to good_frbs_full_params.csv")

# Generate and print summary of good FRBs
good_frbs_output = f"\nFRBs satisfying all conditions: {good_frbs_count} FRBs\n"
good_frbs_output += "These FRBs have:\n"
good_frbs_output += "1. |ΔDMMW| <= 50 pc cm−3 (not Case 1 or Case 2)\n"
good_frbs_output += "2. DMMW < 100 pc cm−3 in both models (not Case 3)\n"

# Add statistics for good FRBs
if not all_good_frbs.empty:
    good_frbs_output += f"\nStatistics for good FRBs:\n"
    good_frbs_output += f"  - Average DM difference: {all_good_frbs['DM_diff'].mean():.2f} pc cm^-3\n"
    good_frbs_output += f"  - Standard deviation: {all_good_frbs['DM_diff'].std():.2f} pc cm^-3\n"
    good_frbs_output += f"  - Min absolute Galactic latitude: {all_good_frbs['abs_GB'].min():.2f} degrees\n"
    good_frbs_output += f"  - Max absolute Galactic latitude: {all_good_frbs['abs_GB'].max():.2f} degrees\n"

print(good_frbs_output)

# Add to the text file
with open('outliers_results.txt', 'a') as f:
    f.write(good_frbs_output)
