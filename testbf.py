import os
import numpy as np
import pandas as pd
import emcee
import corner
import matplotlib.pyplot as plt
from scipy import integrate
from astropy import units as u
from astropy.table import Table
from astropy.cosmology import Planck18 as P
from astropy import constants as con
from scipy.stats import lognorm
from multiprocessing import Pool

# 2. 辅助函数

def sigma_delta_igm(z):
    """计算IGM色散量的涨落误差"""
    return 173.8 * z**0.4

def calculate_dm_igm_obs(dm_obs, dm_mw_ism, z, e_mu, sigma_host, dm_mw_halo=50.0):
    """计算修正后的IGM色散量 (DM_IGM_obs)，宿主 DM 从对数正态分布采样并校正红移"""
    # 从对数正态分布采样宿主 DM_com
    dm_host_com = lognorm(s=sigma_host, scale=e_mu).rvs(size=len(z))
    dm_host_com = [i for i in dm_host_com if i > 0]
    # 校正到观测层面
    dm_host = dm_host_com / (1 + z)
    return dm_obs - dm_mw_ism - dm_mw_halo - dm_host

def calculate_sigma_dm_igm_obs(sigma_obs, z, e_mu, sigma_host):
    """计算观测IGM色散量总误差 (sigma_DM_IGM_obs)，加入宿主 DM 对数正态分布贡献"""
    sigma_mw = 30  # pc/cm³
    sigma_igm = sigma_delta_igm(z)
    # 宿主 DM 对数正态分布方差 var_host = e^(2μ+σ^2)*(e^{σ^2}-1)
    var_host = np.exp(2 * np.log(e_mu) + sigma_host**2) * (np.exp(sigma_host**2) - 1)
    # 折算到观测层面
    sigma_host_corr = np.sqrt(var_host) / (1 + z)
    error_squared = (
        sigma_obs**2 +
        sigma_mw**2 +
        sigma_igm**2 +
        sigma_host_corr**2
    )
    return np.sqrt(error_squared)

def dmigm_integrand(z_prime, alpha,f_igm_0, fe=7/8.,):
    figm = f_igm_0 * (1 + alpha * z_prime / (1 + z_prime))
    y = (1+z_prime)*figm*fe / (P.H(z_prime)/P.H0)
    return y

def calculate_dm_igm_th(z, f_igm_0, alpha):
    A = 3 * con.c * P.Ob(0) * P.H0 / (8 * np.pi * con.G * con.m_p)
    # The integrate.quad function returns a tuple (value, error), we need the value [0]
    val = (A * integrate.quad(dmigm_integrand, 0, z, args=(alpha, f_igm_0))[0]).to(u.pc * u.cm**-3).value
    return val



# 2. 加载数据
def load_data():
    """读取数据"""
    table = Table.read('Select localized FRBs DM with dL.csv')

    # 应用筛选条件
    mask = (table['DM_obs'].value - table['DM_MW_ISM'].value) > 80
    table = table[mask]

    # 提取数据
    z = table['z'].value
    dm_obs = table['DM_obs'].value
    sigma_dm_obs = table['sigma_DM_obs'].value
    dm_mw_ism = table['DM_MW_ISM'].value
    dL_obs = table['dL'].value         # dL 单位为 Mpc
    sigma_dL_obs = table['dL_error'].value # dL_error 单位为 Mpc

    return z, dm_obs, sigma_dm_obs, dm_mw_ism, dL_obs, sigma_dL_obs



# 3. 定义先验
def log_prior(theta):
    f_IGM_0, alpha, e_mu, sigma_host = theta
    # 使用用户指定的先验边界
    if 0.0 < f_IGM_0 < 1.0 and -2.0 < alpha < 2.0 and 20.0 < e_mu < 200.0 and 0.2 < sigma_host < 2.0:
        return 0.0
    return -np.inf
# 4. 定义模型
def log_prob(theta, dm_obs, dm_mw_ism, z, sigma_dm_obs, dL_obs, sigma_dL_obs):
    """全局 log_prob，用于并行计算，接收数据作为参数"""
    f_igm_0, alpha, e_mu, sigma_host = theta
    # 检查先验
    if not (0.0 < f_igm_0 < 1.0 and -2.0 < alpha < 2.0 and 20.0 < e_mu < 200.0 and 0.2 < sigma_host < 2.0):
        return -np.inf
    # 计算修正后的IGM观测DM及误差
    dm_igm_obs = calculate_dm_igm_obs(dm_obs, dm_mw_ism, z, e_mu, sigma_host)
    sigma_dm_igm = calculate_sigma_dm_igm_obs(sigma_dm_obs, z, e_mu, sigma_host)
    # 计算观测R及其误差
    R_obs = dL_obs / dm_igm_obs
    sigma_R = np.sqrt((sigma_dL_obs / dm_igm_obs)**2 + (dL_obs * sigma_dm_igm / dm_igm_obs**2)**2)
    # 理论值计算
    dm_igm_th = np.array([calculate_dm_igm_th(zi, f_igm_0, alpha) for zi in z])
    dL_th = P.luminosity_distance(z).to(u.Mpc).value
    R_th = dL_th / dm_igm_th
    return -0.5 * np.sum(((R_obs - R_th) / sigma_R)**2 )
# 5. 定义后验概率
def main():
    # 加载观测数据
    z, dm_obs, sigma_dm_obs, dm_mw_ism, dL_obs, sigma_dL_obs = load_data()
    # MCMC 参数设置
    ndim = 4
    nwalkers = 32
    # 使用先验区间均匀初始化 walker
    pos = np.vstack([
        np.random.uniform(0.0, 1.0, size=nwalkers),     # f_IGM_0 ∈ [0,1]
        np.random.uniform(-2.0, 2.0, size=nwalkers),    # alpha ∈ [-2,2]
        np.random.uniform(20.0, 200.0, size=nwalkers),  # e_mu ∈ [20,200]
        np.random.uniform(0.2, 2.0, size=nwalkers)      # sigma_host ∈ [0.2,2.0]
    ]).T

    # 运行 emcee 采样
    with Pool() as pool:
        sampler = emcee.EnsembleSampler(nwalkers, ndim, log_prob, args=(dm_obs, dm_mw_ism, z, sigma_dm_obs, dL_obs, sigma_dL_obs), pool=pool)
        sampler.run_mcmc(pos, 1000, progress=True)

    # 扔掉 burn-in，展平链
    flat_samples = sampler.get_chain(discard=100, thin=10, flat=True)
    # 计算最佳拟合参数(中位数)
    best_fit = np.median(flat_samples, axis=0)

    # 绘制 Corner 图
    labels = ["f_IGM_0", "alpha", "e_mu", "sigma_host"]
    fig = corner.corner(flat_samples, labels=labels, truths=best_fit, color='steelblue', quantiles=[0.16, 0.5, 0.84], show_titles=True, title_kwargs={"fontsize":12})
    corner_axes = np.array(fig.get_axes()).reshape(ndim, ndim) # Use ndim
    for ax in np.diag(corner_axes):
        ax.spines['top'].set_visible(True)
        ax.spines['left'].set_visible(True)
        ax.spines['right'].set_visible(True)
        ax.xaxis.set_ticks_position('bottom')
        ax.yaxis.set_ticks_position('none')
    fig.savefig("corner_plot.png")

    # 绘制链图
    fig2, axes = plt.subplots(ndim, figsize=(10, 7), sharex=True)
    samples = sampler.get_chain()
    for i in range(ndim):
        ax = axes[i]
        ax.plot(samples[:, :, i], color="k", alpha=0.3)
        ax.set_ylabel(labels[i])
    axes[-1].set_xlabel("Step number")
    fig2.savefig("chain_plot.png")
    plt.show()

if __name__ == '__main__':
    main() 


